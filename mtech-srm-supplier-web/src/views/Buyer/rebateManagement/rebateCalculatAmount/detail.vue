<template>
  <div class="full-height">
    <!-- 头部表单 -->
    <div class="top-container toggle-container" :class="[!isExpand && 'top-hidden']">
      <div class="top-content">
        <div class="top-btns">
          <vxe-button
            v-for="item in toolbar"
            v-show="!item.isHidden"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :disabled="item.disabled"
            :loading="loadingMap[item.code]"
            size="small"
            @click="handleClickToolBar(item)"
            >{{ item.name }}</vxe-button
          >
        </div>
        <div class="top-form">
          <mt-form ref="dataFormRef" :model="dataForm">
            <mt-form-item prop="rebateCode" :label="$t('返利协议单号')" label-style="top">
              <mt-input v-model="dataForm.rebateCode" disabled />
            </mt-form-item>
            <mt-form-item prop="rebateName" :label="$t('返利协议名称')" label-style="top">
              <mt-input
                v-model="dataForm.rebateName"
                clearable
                type="text"
                maxlength="50"
                disabled
                :placeholder="$t('请输入返利协议名称')"
              />
            </mt-form-item>
            <mt-form-item prop="agreementTemplateCode" :label="$t('协议书模板')" label-style="top">
              <mt-select
                v-model="dataForm.agreementTemplateCode"
                :data-source="agreementTemplateList"
                :show-clear-button="true"
                :allow-filtering="true"
                :fields="{ text: 'text', value: 'agreementCode' }"
                disabled
                :placeholder="$t('请选择协议书模板')"
                @change="(e) => handleValueChange('agreementTemplateCode', e)"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <RemoteAutocomplete
                disabled
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="dataForm.companyCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
                @change="(e) => handleValueChange('companyCode', e)"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="factoryCodeList" :label="$t('工厂')" label-style="top">
              <mt-multi-select
                v-model="dataForm.factoryCodeList"
                :show-clear-button="true"
                :data-source="factoryList"
                disabled
                :fields="{ text: 'text', value: 'siteCode' }"
                :show-select-all="true"
                :placeholder="$t('请选择工厂')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <RemoteAutocomplete
                disabled
                v-model="dataForm.supplierCode"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="supplier"
                @change="(e) => handleValueChange('supplierCode', e)"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="startDate" :label="$t('返利起始日')" label-style="top">
              <mt-date-picker
                v-model="dataForm.startDate"
                clearable
                disabled
                :allow-edit="false"
                :open-on-focus="true"
                :render-day-cell="(args) => handleRenderDayCell('start', args)"
                :placeholder="$t('请选择返利起始日')"
              />
            </mt-form-item>
            <mt-form-item prop="endDate" :label="$t('返利结束日')" label-style="top">
              <mt-date-picker
                v-model="dataForm.endDate"
                clearable
                disabled
                :allow-edit="false"
                :open-on-focus="true"
                :render-day-cell="(args) => handleRenderDayCell('end', args)"
                :placeholder="$t('请选择返利结束日')"
              />
            </mt-form-item>
            <mt-form-item prop="currency" :label="$t('币种')" label-style="top">
              <mt-select
                v-model="dataForm.currency"
                :data-source="currencyList"
                :show-clear-button="true"
                :allow-filtering="true"
                disabled
                filter-type="Contains"
                :fields="{ text: 'text', value: 'currencyCode' }"
                :placeholder="$t('请选择币种')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="dataForm.status"
                :data-source="statusList"
                disabled
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierHandleRemark" :label="$t('供方处理意见')" label-style="top">
              <mt-select
                v-model="dataForm.supplierHandleRemark"
                :data-source="handleRemarkList"
                disabled
              />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="taxNumber"
              :label="$t('购买方税号')"
              label-style="top"
            >
              <mt-input v-model="dataForm.taxNumber" disabled />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="writeOffAmount"
              :label="$t('冲销金额')"
              label-style="top"
            >
              <mt-input
                v-model="dataForm.writeOffAmount"
                :disabled="dataForm.status !== 9 || tabIndex !== 0"
                type="number"
                min="0"
              />
            </mt-form-item>
            <mt-form-item
              v-if="showKtField"
              prop="writeOffDesc"
              :label="$t('冲销说明')"
              label-style="top"
            >
              <mt-input
                v-model="dataForm.writeOffDesc"
                :disabled="dataForm.status !== 9 || tabIndex !== 0"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </div>
      <div class="top-toggle-btn">
        <div @click="handleToggle"><i class="vxe-icon-arrow-up" /></div>
      </div>
    </div>
    <!-- 内容部分table -->
    <div class="body-container">
      <mt-tabs :e-tab="false" :data-source="tabList" @handleSelectTab="handleSelectTab" />
      <!-- 返利金额详情 -->
      <div v-if="currentTabIndex === 0" class="table-item">
        <detail-table
          ref="detailRef"
          :detail-info="dataForm"
          :list="detailList"
          @refresh="getDetailInfo"
        />
      </div>
      <!-- 附件列表 -->
      <div v-if="currentTabIndex === 1">
        <!-- 采方附件 -->
        <div class="table-item">
          <div class="item-top">
            <div class="item-title">
              <div class="title-prefix" />
              {{ $t('采方附件') }}
            </div>
            <div
              class="item-icon"
              :class="[!purIsExpand && 'item-icon-hidden']"
              @click="purIsExpand = !purIsExpand"
            >
              <i class="vxe-icon-arrow-double-right" />
            </div>
          </div>
          <purchase-attachment
            v-show="purIsExpand"
            ref="purAttachmentRef"
            :detail-info="dataForm"
            :list="purAttachmentList"
          />
        </div>
        <!-- 供方附件 -->
        <div class="table-item">
          <div class="item-top">
            <div class="item-title">
              <div class="title-prefix" />
              {{ $t('供方附件') }}
            </div>
            <div
              class="item-icon"
              :class="[!supIsExpand && 'item-icon-hidden']"
              @click="supIsExpand = !supIsExpand"
            >
              <i class="vxe-icon-arrow-double-right" />
            </div>
          </div>
          <supplier-attachment
            v-show="supIsExpand"
            ref="supAttachmentRef"
            :list="supAttachmentList"
          />
        </div>
        <!-- 冲销附件 -->
        <div v-if="showKtField" class="table-item">
          <div class="item-top">
            <div class="item-title">
              <div class="title-prefix" />
              {{ $t('冲销附件') }}
            </div>
            <div
              class="item-icon"
              :class="[!writeOffIsExpand && 'item-icon-hidden']"
              @click="writeOffIsExpand = !writeOffIsExpand"
            >
              <i class="vxe-icon-arrow-double-right" />
            </div>
          </div>
          <write-off-attachment
            v-show="writeOffIsExpand"
            ref="writeOffAttachmentRef"
            :detail-info="dataForm"
            :list="writeOffAttachmentList"
            @refresh="getDetailInfo"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { statusList, handleRemarkList } from './config/index'
import DetailTable from './components/detailTable.vue'
import PurchaseAttachment from './components/purchaseAttachment.vue'
import SupplierAttachment from './components/supplierAttachment.vue'
import WriteOffAttachment from './components/writeOffAttachment.vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getCurrentBu } from '@/constants/bu'

export default {
  components: { DetailTable, PurchaseAttachment, SupplierAttachment, WriteOffAttachment, RemoteAutocomplete },
  data() {
    return {
      type: 'detail',
      isExpand: true,
      dataForm: {
        factoryCodeList: [],
        status: 1
      },
      statusList,
      handleRemarkList,
      agreementTemplateList: [],
      factoryList: [],
      currencyList: [],
      purIsExpand: true,
      supIsExpand: true,
      writeOffIsExpand: true,
      detailList: [], //明细列表
      purAttachmentList: [], //采方附件
      supAttachmentList: [], // 供方附件
      writeOffAttachmentList: [], // 冲销附件
      isInit: true,
      currentTabIndex: 0,
      tabList: [
        {
          title: this.$t('返利金额明细')
        },
        {
          title: this.$t('附件')
        }
      ],
      loadingMap: {
        pushToFSSC: false
      }
    }
  },
  computed: {
    tabIndex() {
      return Number(this.$route.query.tabIndex)
    },
    editable() {
      return [1, 3, 6].includes(this.dataForm.status)
    },
    // 空调事业部专有字段显示控制
    showKtField() {
      return getCurrentBu() === 'kt'
    },
    toolbar() {
      const toolbar = [
        {
          code: 'pushToFSSC',
          name: this.$t('推送共享'),
          status: '',
          isHidden: [1, 2].includes(this.tabIndex)
        },
        {
          code: 'save',
          name: this.$t('保存'),
          isHidden: [1].includes(this.tabIndex)
        },
        {
          code: 'confirm',
          name: this.$t('确认金额明细'),
          isHidden:
            [1, 2].includes(this.tabIndex) ||
            !([9].includes(this.dataForm.status) && this.showKtField)
        },
        {
          code: 'back',
          name: this.$t('返回'),
          status: 'primary'
        }
      ]
      return toolbar
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 获取币种下拉
      this.getCurrencyList()
      if (this.$route.query.type !== 'create') {
        this.getDetailInfo()
      }
    },
    // 获取明细信息
    async getDetailInfo() {
      this.isInit = true
      const res = await this.$API.rebateManagement.getRebateAmountItemDetailById({
        id: this.$route.query.id,
        queryType: this.$route.query.tabIndex == 0 ? 1 : 2
      })
      if (res.code === 200) {
        const {
          siteInfo,
          startDate,
          endDate,
          feedbackDate,
          itemResponseList,
          purchaseFileList,
          supplierFileList,
          writeOffFileList
        } = res.data

        const factoryCodeList = []
        const factoryList = siteInfo ? JSON.parse(siteInfo) : []
        factoryList.forEach((item) => factoryCodeList.push(item.code))
        this.dataForm = {
          ...res.data,
          factoryCodeList,
          startDate: startDate ? dayjs(Number(startDate)).format('YYYY-MM-DD') : null,
          endDate: endDate ? dayjs(Number(endDate)).format('YYYY-MM-DD') : null,
          feedbackDate: feedbackDate ? dayjs(Number(feedbackDate)).format('YYYY-MM-DD') : null
        }
        this.detailList = itemResponseList
        this.purAttachmentList = purchaseFileList
        this.supAttachmentList = supplierFileList
        this.writeOffAttachmentList = writeOffFileList || []

        this.getAgreementTemplateList(res.data.companyCode)
        this.getFactoryList(res.data.companyCode)
      }
    },
    // 协议模板下拉列表
    async getAgreementTemplateList(companyCode) {
      const params = {
        status: 1,
        companyCode
      }
      const res = await this.$API.rebateManagement.queryAgreemenTempalteList(params)
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.agreementCode + '-' + item.agreementName
        })
        this.agreementTemplateList = res.data || []
      }
    },
    // 工厂下拉列表
    async getFactoryList(companyCode) {
      const params = {
        condition: '',
        page: {
          current: 1,
          size: 1000
        },
        defaultRules: [
          {
            label: this.$t('公司'),
            field: 'parentCode',
            type: 'string',
            operator: 'equal',
            value: companyCode
          }
        ],
        pageFlag: false
      }

      const res = await this.$API.masterData.getSiteList(params)
      if (res.code === 200) {
        res.data.records?.forEach((item) => (item.text = item.siteCode + '-' + item.siteName))
        this.factoryList = res.data.records
      }
    },
    // 获取币种下拉列表
    async getCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency()
      if (res.code === 200) {
        res.data?.forEach((item) => {
          item.text = item.currencyCode + '-' + item.currencyName
        })
        this.currencyList = res.data
      }
    },
    // 限制日期选择器选择
    handleRenderDayCell(type, args) {
      const day = dayjs(args.date).get('date')
      const lastDay = dayjs(args.date).endOf('month').$D
      if (type === 'end' && day !== lastDay) {
        args.isDisabled = true
        return
      }
      if (type === 'start' && day !== 1) {
        args.isDisabled = true
        return
      }
    },
    // 展开/收缩
    handleToggle() {
      this.isExpand = !this.isExpand
    },
    // 下拉列表选中值修改
    handleValueChange(prefix, e) {
      const { value, itemData } = e
      switch (prefix) {
        // 选择公司
        case 'companyCode':
          this.dataForm.companyName = itemData?.orgName

          if (value) {
            this.getAgreementTemplateList(value)
            this.getFactoryList(value)
          } else {
            this.agreementTemplateList = []
            this.factoryList = []
          }

          if (!this.isInit) {
            this.$set(this.dataForm, 'agreementTemplateCode', null)
            this.$set(this.dataForm, 'agreementTemplateName', null)

            this.$set(this.dataForm, 'factoryCodeList', [])
          }
          break
        case 'agreementTemplateCode':
          this.$set(this.dataForm, 'agreementTemplateName', itemData?.agreementName || null)
          break
        case 'supplierCode':
          this.$set(this.dataForm, 'supplierName', itemData?.supplierName || null)
          break
        default:
          break
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      switch (e.code) {
        case 'back':
          this.$router.go(-1)
          break
        case 'save':
          this.handleSave()
          break
        case 'pushToFSSC':
          this.handlePushToFSSC()
          break
        case 'confirm':
          this.handleConfirmAmountDetail()
          break
        default:
          break
      }
    },
    async handlePushToFSSC() {
      const params = this.$refs.detailRef.$refs.scTableRef.$refs.xGrid.getCheckboxRecords()
      // for (let item of params) {
      //   if (item.status !== 3) {
      //     this.$toast({
      //       content: this.$t('所选金额明细不是供应商已确认状态，无法推送！'),
      //       type: 'warning'
      //     })
      //     return
      //   }
      // }
      const idList = params.map((e) => e.id)
      this.loadingMap.pushToFSSC = true
      const res = await this.$API.rebateManagement
        .pushToFSSC(idList)
        .catch(() => (this.loadingMap.pushToFSSC = false))
      this.loadingMap.pushToFSSC = false
      if (res.code === 200) {
        this.$toast({ content: this.$t('推送成功'), type: 'success' })
        this.getDetailInfo()
      }
    },
    // 保存、保存后发布
    async handleSave() {
      const params = {
        status: this.dataForm.supplierHandleRemark,
        agreementCode: this.dataForm.rebateCode,
        purchaseFileList:
          this.currentTabIndex === 1
            ? this.$refs.purAttachmentRef?.dataList
            : this.purAttachmentList
      }
      const res = await this.$API.rebateManagement.rebateAmountSaveItem(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('保存成功'), type: 'success' })
        this.$router.replace({
          name: 'rebate-calculat-amount'
        })
      }
    },
    handleSelectTab(index) {
      if (index === 0) {
        this.purAttachmentList = this.$refs.purAttachmentRef?.dataList
        this.writeOffAttachmentList = this.$refs.writeOffAttachmentRef?.dataList || this.writeOffAttachmentList
      }
      this.currentTabIndex = index
    },
    // 确认金额明细
    async handleConfirmAmountDetail() {
      try {
        // 获取选中的轮次
        let roundList = []

        if (this.showKtField) {
          // 空调事业部：从detailTable组件获取选中的轮次
          const selectedRounds = this.$refs.detailRef?.selectedRounds || []
          if (selectedRounds.length === 0) {
            this.$toast({ content: this.$t('请选择要确认的轮次'), type: 'warning' })
            return
          }
          roundList = selectedRounds
        }

        // 必填校验：检查roundList是否为空
        if (roundList.length === 0) {
          this.$toast({ content: this.$t('请选择要确认的轮次'), type: 'warning' })
          return
        }

        // 确认对话框
        this.$dialog({
          data: {
            title: this.$t('确认'),
            message: this.$t(`确定要确认第 ${roundList.join('、')} 轮的金额明细吗？`)
          },
          success: async () => {
            await this.confirmAmountDetailAPI(roundList)
          }
        })
      } catch (error) {
        console.error('确认金额明细失败:', error)
        this.$toast({ content: error.msg || this.$t('确认金额明细失败'), type: 'error' })
      }
    },
    // 调用确认金额明细API
    async confirmAmountDetailAPI(roundList) {
      try {
        // 获取冲销附件数据
        const writeOffFileList = this.currentTabIndex === 1
          ? this.$refs.writeOffAttachmentRef?.dataList || this.writeOffAttachmentList
          : this.writeOffAttachmentList

        const params = {
          id: this.dataForm.id || this.$route.query.id,
          roundList: roundList,
          writeOffAmount: this.dataForm.writeOffAmount || 0,
          writeOffDesc: this.dataForm.writeOffDesc || '',
          writeOffFileList: writeOffFileList || []
        }

        const res = await this.$API.rebateManagement.purchaseConfirmAmountItem(params)
        if (res.code === 200) {
          this.$toast({ content: this.$t('确认金额明细成功'), type: 'success' })
          // 刷新页面数据
          this.getDetailInfo()
        } else {
          this.$toast({ content: res.msg || this.$t('确认金额明细失败'), type: 'error' })
        }
      } catch (error) {
        console.error('调用确认金额明细API失败:', error)
        this.$toast({ content: error.msg || this.$t('确认金额明细失败'), type: 'error' })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.full-height {
  width: 100%;
  height: auto;
  padding: 8px;
  background: #fff;
  overflow: scroll;
}

.top-container {
  width: 100%;
  .top-content {
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 8px;
    .top-btns {
      text-align: right;
      margin-bottom: 10px;
    }
    .top-form {
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 5 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
    }
  }
  .top-toggle-btn {
    width: 100%;
    div {
      width: 80px;
      margin: auto;
      text-align: center;
      background-color: #4a556b;
      color: #fff;
      font-weight: bold;
      border-radius: 0 0 8px 8px;
    }
  }
}

.top-hidden {
  .top-form {
    display: none;
  }
  .top-toggle-btn {
    div {
      transform: rotate(180deg);
      border-radius: 8px 8px 0 0;
    }
  }
}

.body-container {
  .table-item {
    width: 100%;
    margin-top: 10px;
    .item-top {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .item-title {
        display: flex;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        font-weight: 500;
        .title-prefix {
          width: 5px;
          height: 18px;
          background-color: #409eff;
          margin: 7px 15px 0 0;
        }
      }

      .item-icon {
        color: #409eff;
        transform: rotate(270deg);
      }
      .item-icon-hidden {
        transform: rotate(90deg);
        margin-right: 16px;
      }
    }
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 15px;
  }

  .j-select.ant-select .ant-select-selection {
    background-color: #f5f5f5;
    border-color: rgba(0, 0, 0, 0.42);
  }
}
</style>
