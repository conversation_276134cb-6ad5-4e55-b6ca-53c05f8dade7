<template>
  <!-- 明细table -->
  <div class="detail-table">
    <!-- 空调事业部轮次选择器 -->
    <div v-if="showKtField && [0].includes(tabIndex)" class="rounds-selector-container">
      <div class="rounds-selector-label">{{ $t('确认轮次') }}：</div>
      <div class="rounds-selector-options">
        <vxe-checkbox-group v-model="selectedRounds" @change="handleRoundSelectionChange">
          <vxe-checkbox
            v-for="round in availableRounds"
            :key="round"
            :label="round"
            :content="`${$t('第')} ${round} ${$t('轮')}`"
          />
        </vxe-checkbox-group>
      </div>
    </div>

    <ScTable
      ref="scTableRef"
      grid-id="d6caf746-b566-4293-a85a-b71210f7f441"
      :columns="columns"
      :table-data="tableData"
      show-footer
      :footer-method="footerMethod"
    >
      <template #custom-tools>
        <vxe-button
          v-for="item in detailToolbar"
          v-show="!item.isHidden"
          :key="item.code"
          v-bind="item"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </ScTable>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import mixin from './config/mixin'
import { getHeadersFileName, download } from '@/utils/utils'
import { CheckboxGroup as VxeCheckboxGroup } from 'vxe-table'

export default {
  components: {
    ScTable,
    VxeCheckboxGroup
  },
  mixins: [mixin],
  data() {
    return {
      type: 'detail',
      selectedRounds: [] // 空调事业部选中的轮次
    }
  },
  computed: {
    tabIndex() {
      return this.$route.query.tabIndex || 0
    },
    // 从表格数据中提取所有可用的轮次（状态为30或12）
    availableRounds() {
      const rounds = [...new Set(
        this.tableData
          .filter((item) => item.status === 30 || item.status === 12)
          .map((item) => item.rounds)
      )].filter((round) => round != null)
      return rounds.sort((a, b) => a - b)
    },
    detailToolbar() {
      let toolbar = []
      if (
        localStorage.getItem('currentBu') === 'GF' &&
        localStorage.getItem('rebatePageType') === 'edit'
      ) {
        toolbar = [
          {
            code: 'recalculate',
            name: i18n.t('金额重算'),
            status: 'info',
            loading: false,
            isHidden: [1].includes(this.tabIndex)
          },
          {
            code: 'submit',
            name: i18n.t('提交OA审批'),
            status: 'info',
            loading: false,
            isHidden: [1].includes(this.tabIndex)
          },
          {
            code: 'export',
            name: i18n.t('导出'),
            status: 'info',
            loading: false
          },
          {
            code: 'check',
            name: i18n.t('查看金额明细审批记录'),
            status: 'info',
            loading: false
          }
        ]
      }
      return toolbar
    },
    dataList() {
      const dataList = []
      this.tableData.forEach((item, index) => {
        if (item.isAdd) {
          item.id = null
        } else if (this.tableRef.isUpdateByRow(item)) {
          item.optType = 'modify'
        }
        dataList.push({
          ...item,
          lineNumber: index + 1,
          actualStartDate: new Date(item.actualStartDate).getTime()
        })
      })
      return dataList
    }
  },
  mounted() {},
  methods: {
    async handleClickToolBar(item) {
      const selectedRecords = this.tableRef.getCheckboxRecords()

      if (selectedRecords.length === 0 && ['recalculate', 'submit', 'check'].includes(item.code)) {
        this.$toast({ content: this.$t('请选择要操作的行'), type: 'warning' })
        return
      }

      if (selectedRecords.length > 1 && ['recalculate', 'submit', 'check'].includes(item.code)) {
        this.$toast({ content: this.$t('只能选择一行进行操作'), type: 'warning' })
        return
      }

      const actionMap = {
        recalculate: () => this.beforeReCalculate(selectedRecords),
        submit: () => this.beforeSubmit(selectedRecords),
        export: () => this.handleExport(item),
        check: () => this.handleCheck(selectedRecords)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },

    showConfirmDialog(action, callback) {
      return this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`${action}`)
        },
        success: () => {
          if (typeof callback === 'function') {
            callback()
          }
        }
      })
    },

    beforeReCalculate(selectedRecords) {
      let flag = selectedRecords.some((v) => v.status !== 12)
      if (flag) {
        this.$toast({ content: this.$t('请选择状态为【金额明细审批驳回】的记录'), type: 'warning' })
        return
      }
      let rebateAmountItemId = selectedRecords[0].id
      let rebateCode = this.detailInfo.rebateCode
      let rounds = selectedRecords[0].rounds
      this.showConfirmDialog(
        `确定要重新触发返利协议 ${rebateCode} 第 ${rounds} 轮金额明细计算吗？重算后将删除当前计算结果`,
        () => this.handleReCalculate(rebateAmountItemId)
      )
    },

    async handleReCalculate(rebateAmountItemId) {
      try {
        const params = {
          rebateAmountItemId
        }
        const res = await this.$API.rebateManagement.rebateAmountItemReCalculateApi(params)
        if (res.code === 200) {
          this.$toast({ content: this.$t('重新计算成功'), type: 'success' })
          this.$emit('refresh')
        }
      } catch (error) {
        console.error(error)
      }
    },

    beforeSubmit(selectedRecords) {
      let rebateAmountItemId = selectedRecords[0].id
      let rebateCode = this.detailInfo.rebateCode
      let rounds = selectedRecords[0].rounds
      this.showConfirmDialog(
        `确定要提交返利协议 ${rebateCode} 第 ${rounds} 轮金额明细到OA吗？`,
        () => this.handleSubmit(rebateAmountItemId)
      )
    },

    async handleSubmit(rebateAmountItemId) {
      try {
        const params = {
          rebateAmountItemId
        }
        const res = await this.$API.rebateManagement.submitRebateAmountItemDetailApi(params)
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交OA成功'), type: 'success' })
          this.$emit('refresh')
        }
      } catch (error) {
        console.error(error)
      }
    },

    async handleCheck(selectedRecords) {
      try {
        let params = {
          rebateHeaderId: this.$route.query?.id,
          rounds: selectedRecords[0].rounds
        }
        const res = await this.$API.rebateManagement.checkRebateAmountItemDetailApi(params)
        if (res.code === 200 && res.data) {
          window.open(res.data)
        } else {
          this.$toast({ content: res.msg, type: 'error' })
        }
      } catch (error) {
        console.error(error)
      }
    },

    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          rebateHeaderId: this.$route.query?.id
        }

        const res = await this.$API.rebateManagement.exportRebateAmountItemDetailApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },
    footerMethod({ columns, data }) {
      const sums = []
      columns.forEach((column) => {
        let sumCell = null
        switch (column.property) {
          case 'settleQty':
          case 'settleAmt':
          case 'amtUntaxed':
          case 'amtTaxed':
            sumCell = `${this.$t('总计')}：${this.sumNum(data, column.property)}`
            break
        }
        sums.push(sumCell)
      })
      // 返回一个二维数组的表尾合计
      return [sums]
    },
    // 求和
    sumNum(list, field) {
      let count = 0
      list.forEach((item) => {
        count += parseFloat(item[field])
      })
      return this.formatAmount(count, 5)
    },
    // 点击'阶梯等级'单元格
    handleClickCellTitle(row, column) {
      switch (column.field) {
        case 'ladderInfo':
          this.$dialog({
            modal: () => import('./stepRankDiaolog.vue'),
            data: {
              title: this.$t('阶梯返利等级查看'),
              id: row.id,
              type: row.rebateType,
              list: row.ladderInfo ? JSON.parse(row.ladderInfo) : []
            },
            success: (list) => {
              row.ladderInfo = JSON.stringify(list)
              const index = this.tableData.findIndex((r) => r.id === row.id)
              this.$set(this.tableData, index, row)
            }
          })
          break
        default:
          break
      }
    }
  }
}
</script>

<style scoped lang="scss">
.detail-table {
  .rounds-selector-container {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;

    .rounds-selector-label {
      font-weight: 500;
      color: #495057;
      margin-right: 16px;
      white-space: nowrap;
    }

    .rounds-selector-options {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      :deep(.vxe-checkbox-group) {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .vxe-checkbox {
          margin-right: 0;

          .vxe-checkbox--label {
            font-size: 14px;
            color: #495057;
          }

          &.is--checked .vxe-checkbox--label {
            color: #409eff;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
