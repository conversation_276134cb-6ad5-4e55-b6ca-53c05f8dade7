

## 采方确认返利金额明细


**接口地址**:`/tenant/rebateHeader/purchaseConfirmAmountItem`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "id": 0,
  "roundList": [],
  "writeOffAmount": 0,
  "writeOffDesc": "",
  "writeOffFileList": [
    {
      "attachmentId": 0,
      "attachmentName": "",
      "attachmentSize": 0,
      "attachmentUrl": "",
      "claimId": 0,
      "id": 0,
      "remark": "",
      "tenantId": 0,
      "uploadTime": 0,
      "uploadUserId": 0,
      "uploadUserName": ""
    }
  ]
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|req|req|body|true|采方返利金额明细确认请求|采方返利金额明细确认请求|
|&emsp;&emsp;id|返利协议ID||false|integer(int64)||
|&emsp;&emsp;roundList|返利轮次||false|array|integer(int32)|
|&emsp;&emsp;writeOffAmount|冲销金额||false|number||
|&emsp;&emsp;writeOffDesc|冲销说明||false|string||
|&emsp;&emsp;writeOffFileList|冲销附件||false|array|考核单附件通用返回|
|&emsp;&emsp;&emsp;&emsp;attachmentId|附件文件id||false|integer||
|&emsp;&emsp;&emsp;&emsp;attachmentName|附件文件名称||false|string||
|&emsp;&emsp;&emsp;&emsp;attachmentSize|附件文件大小||false|number||
|&emsp;&emsp;&emsp;&emsp;attachmentUrl|附件文件路径||false|string||
|&emsp;&emsp;&emsp;&emsp;claimId|考核单id||false|integer||
|&emsp;&emsp;&emsp;&emsp;id|主键||false|integer||
|&emsp;&emsp;&emsp;&emsp;remark|备注||false|string||
|&emsp;&emsp;&emsp;&emsp;tenantId|租户ID||false|integer||
|&emsp;&emsp;&emsp;&emsp;uploadTime|上传时间||false|integer||
|&emsp;&emsp;&emsp;&emsp;uploadUserId|上传用户id||false|integer||
|&emsp;&emsp;&emsp;&emsp;uploadUserName|上传用户名称||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|通用返回统一封装对象«string»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|响应编码|integer(int32)|integer(int32)|
|data|数据|string||
|errorStackTrace|错误堆栈信息|string||
|msg|提示信息|string||
|success|是否成功|boolean||
|traceId|追踪ID|string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": "",
	"errorStackTrace": "",
	"msg": "",
	"success": true,
	"traceId": ""
}
```